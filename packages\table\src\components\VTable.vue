<template>
  <div class="vtable-wrapper">
    <!-- 加载状态 -->
    <div v-if="loading" class="vtable-loading">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>

    <!-- VTable 容器 -->
    <div
      ref="tableContainer"
      class="vtable-container"
      :class="{ 'vtable-hidden': loading }"
      :style="{ width: width + 'px', height: height + 'px' }"
    ></div>

    <!-- 编辑器弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="`编辑 ${editingColumn?.title || ''}`"
      width="400px"
      :before-close="handleEditCancel"
    >
      <div class="edit-form">
        <!-- 文本输入 -->
        <el-input
          v-if="editingColumn?.fieldType === 'text'"
          v-model="editValue"
          :placeholder="editingColumn?.placeholder"
          @keyup.enter="handleEditConfirm"
        />

        <!-- 数字输入 -->
        <el-input-number
          v-else-if="editingColumn?.fieldType === 'number'"
          v-model="editValue"
          :min="editingColumn?.min"
          :max="editingColumn?.max"
          :step="editingColumn?.step || 1"
          :precision="editingColumn?.precision"
          :placeholder="editingColumn?.placeholder"
          style="width: 100%"
        />

        <!-- 单选下拉 -->
        <el-select
          v-else-if="editingColumn?.fieldType === 'select'"
          v-model="editValue"
          :placeholder="editingColumn?.placeholder || '请选择'"
          style="width: 100%"
        >
          <el-option
            v-for="option in editingColumn?.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          />
        </el-select>

        <!-- 多选下拉 -->
        <el-select
          v-else-if="editingColumn?.fieldType === 'multiSelect'"
          v-model="editValue"
          multiple
          :placeholder="editingColumn?.placeholder || '请选择'"
          style="width: 100%"
        >
          <el-option
            v-for="option in editingColumn?.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          />
        </el-select>

        <!-- 复选框 -->
        <el-checkbox
          v-else-if="editingColumn?.fieldType === 'checkbox'"
          v-model="editValue"
        >
          {{ editingColumn?.placeholder || '选中' }}
        </el-checkbox>

        <!-- 日期选择 -->
        <el-date-picker
          v-else-if="editingColumn?.fieldType === 'date'"
          v-model="editValue"
          type="date"
          :placeholder="editingColumn?.placeholder || '选择日期'"
          :format="editingColumn?.format || 'YYYY-MM-DD'"
          style="width: 100%"
        />

        <!-- 日期时间选择 -->
        <el-date-picker
          v-else-if="editingColumn?.fieldType === 'datetime'"
          v-model="editValue"
          type="datetime"
          :placeholder="editingColumn?.placeholder || '选择日期时间'"
          :format="editingColumn?.format || 'YYYY-MM-DD HH:mm:ss'"
          style="width: 100%"
        />

        <!-- 链接输入 -->
        <el-input
          v-else-if="editingColumn?.fieldType === 'link'"
          v-model="editValue"
          :placeholder="editingColumn?.placeholder || '请输入链接'"
          @keyup.enter="handleEditConfirm"
        />

        <!-- 邮箱输入 -->
        <el-input
          v-else-if="editingColumn?.fieldType === 'email'"
          v-model="editValue"
          type="email"
          :placeholder="editingColumn?.placeholder || '请输入邮箱'"
          @keyup.enter="handleEditConfirm"
        />

        <!-- 电话输入 -->
        <el-input
          v-else-if="editingColumn?.fieldType === 'phone'"
          v-model="editValue"
          :placeholder="editingColumn?.placeholder || '请输入电话'"
          @keyup.enter="handleEditConfirm"
        />

        <!-- 默认文本输入 -->
        <el-input
          v-else
          v-model="editValue"
          :placeholder="editingColumn?.placeholder"
          @keyup.enter="handleEditConfirm"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleEditCancel">取消</el-button>
          <el-button type="primary" @click="handleEditConfirm">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ListTable } from '@visactor/vtable'
import type {
  VTableProps,
  VTableColumn,
  CellEditEvent,
  CellValidationResult
} from '../types'

// Props
const props = withDefaults(defineProps<VTableProps>(), {
  height: 400,
  width: 800,
  loading: false,
  editable: true,
  striped: true,
  bordered: true,
  showHeader: true,
  frozenColCount: 0,
  frozenRowCount: 0,
  allowResizeColumn: true,
  allowSortColumn: true,
  theme: 'default'
})

// Emits
const emit = defineEmits<{
  cellEdit: [event: CellEditEvent]
  cellValidate: [field: string, value: any, callback: (result: CellValidationResult) => void]
}>()

// 响应式数据
const tableContainer = ref<HTMLElement>()
const tableInstance = ref<ListTable>()
const editDialogVisible = ref(false)
const editValue = ref<any>()
const editingCell = ref<{
  rowIndex: number
  colIndex: number
  field: string
  oldValue: any
}>()
const editingColumn = ref<VTableColumn>()

// 初始化表格
const initTable = async () => {
  if (!tableContainer.value) return

  // 销毁现有实例
  if (tableInstance.value) {
    tableInstance.value.release()
  }

  await nextTick()

  // 转换列配置 - 使用简单的列配置
  const columns = props.columns.map(col => ({
    field: col.field,
    title: col.title,
    width: col.width || 120
  }))

  // 创建表格实例
  const option = {
    container: tableContainer.value,
    columns,
    records: props.data
  }

  tableInstance.value = new ListTable(option as any)

  // 绑定事件
  bindEvents()
}

// 获取单元格类型
const getCellType = (fieldType?: string) => {
  switch (fieldType) {
    case 'checkbox':
      return 'checkbox'
    case 'link':
      return 'link'
    default:
      return 'text'
  }
}

// 获取主题配置
const getTheme = () => {
  const themes = {
    default: 'DEFAULT',
    dark: 'DARK',
    bright: 'BRIGHT'
  }
  return themes[props.theme] || 'DEFAULT'
}

// 绑定事件
const bindEvents = () => {
  if (!tableInstance.value) return

  // 双击编辑事件
  tableInstance.value.on('dblclick_cell', (args: any) => {
    if (!props.editable) return

    const { col, row } = args
    const column = props.columns[col]

    if (!column?.editable) return

    // 设置编辑状态
    editingCell.value = {
      rowIndex: row,
      colIndex: col,
      field: column.field,
      oldValue: props.data[row]?.[column.field]
    }
    editingColumn.value = column
    editValue.value = getEditValue(props.data[row]?.[column.field], column.fieldType)
    editDialogVisible.value = true
  })
}

// 获取编辑值
const getEditValue = (value: any, fieldType?: string) => {
  if (value === null || value === undefined) {
    return fieldType === 'checkbox' ? false :
           fieldType === 'multiSelect' ? [] : ''
  }
  return value
}

// 处理编辑确认
const handleEditConfirm = async () => {
  if (!editingCell.value || !editingColumn.value) return

  const { rowIndex, colIndex, field, oldValue } = editingCell.value
  const newValue = editValue.value

  try {
    // 验证数据
    if (editingColumn.value.validator) {
      const validationResult = editingColumn.value.validator(newValue)
      if (validationResult !== true) {
        ElMessage.error(typeof validationResult === 'string' ? validationResult : '数据验证失败')
        return
      }
    }

    // 必填验证
    if (editingColumn.value.required && (newValue === '' || newValue === null || newValue === undefined)) {
      ElMessage.error(`${editingColumn.value.title} 为必填项`)
      return
    }

    // 更新数据
    const record = props.data[rowIndex]
    if (record) {
      record[field] = newValue

      // 刷新表格
      tableInstance.value?.setRecords(props.data)

      // 触发编辑事件
      const editEvent: CellEditEvent = {
        field,
        rowIndex,
        colIndex,
        oldValue,
        newValue,
        record
      }
      emit('cellEdit', editEvent)

      ElMessage.success('编辑成功')
    }

    // 关闭编辑对话框
    editDialogVisible.value = false
    resetEditState()
  } catch (error) {
    console.error('编辑失败:', error)
    ElMessage.error('编辑失败，请重试')
  }
}

// 处理编辑取消
const handleEditCancel = () => {
  editDialogVisible.value = false
  resetEditState()
}

// 重置编辑状态
const resetEditState = () => {
  editingCell.value = undefined
  editingColumn.value = undefined
  editValue.value = undefined
}

// 监听数据变化
watch(() => props.data, () => {
  if (tableInstance.value) {
    tableInstance.value.setRecords(props.data)
  }
}, { deep: true })

watch(() => props.columns, () => {
  initTable()
}, { deep: true })

// 生命周期
onMounted(() => {
  initTable()
})

onUnmounted(() => {
  if (tableInstance.value) {
    tableInstance.value.release()
  }
})
</script>

<style scoped>
.vtable-wrapper {
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.vtable-container {
  width: 100%;
  height: 100%;
}

.vtable-hidden {
  visibility: hidden;
}

.vtable-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.edit-form {
  padding: 16px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
