<template>
  <div class="vtable-wrapper">
    <!-- 加载状态 -->
    <div v-if="loading" class="vtable-loading">
      <div class="loading-spinner" />
      <span>加载中...</span>
    </div>

    <!-- VTable 组件 -->
    <ListTable
      v-if="!loading"
      ref="tableRef"
      :options="tableOptions"
      :records="data"
      :width="width"
      :height="height"
      @on-dbl-click-cell="handleDblClickCell"
      @on-click-cell="handleClickCell"
    />

    <!-- 内联编辑器 -->
    <div
      v-if="editingCell"
      ref="inlineEditor"
      class="inline-editor"
      :style="editorStyle"
    >
      <!-- 文本输入 -->
      <input
        v-if="editingColumn?.fieldType === 'text'"
        ref="editorInput"
        v-model="editValue"
        type="text"
        class="editor-input"
        :placeholder="editingColumn?.placeholder"
        @keyup.enter="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      />

      <!-- 数字输入 -->
      <input
        v-else-if="editingColumn?.fieldType === 'number'"
        ref="editorInput"
        v-model="editValue"
        type="number"
        class="editor-input"
        :min="editingColumn?.min"
        :max="editingColumn?.max"
        :step="editingColumn?.step || 1"
        :placeholder="editingColumn?.placeholder"
        @keyup.enter="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      />

      <!-- 单选下拉 -->
      <select
        v-else-if="editingColumn?.fieldType === 'select'"
        ref="editorInput"
        v-model="editValue"
        class="editor-select"
        @change="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      >
        <option value="">{{ editingColumn?.placeholder || '请选择' }}</option>
        <option
          v-for="option in editingColumn?.options"
          :key="option.value"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </option>
      </select>

      <!-- 多选下拉 -->
      <select
        v-else-if="editingColumn?.fieldType === 'multiSelect'"
        ref="editorInput"
        v-model="editValue"
        multiple
        class="editor-select editor-multiselect"
        @change="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      >
        <option
          v-for="option in editingColumn?.options"
          :key="option.value"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </option>
      </select>

      <!-- 复选框 -->
      <label
        v-else-if="editingColumn?.fieldType === 'checkbox'"
        class="editor-checkbox"
        @click="handleCheckboxClick"
      >
        <input
          ref="editorInput"
          v-model="editValue"
          type="checkbox"
          @keyup.escape="handleEditCancel"
          @blur="handleEditConfirm"
        />
        {{ editingColumn?.placeholder || '选中' }}
      </label>

      <!-- 日期选择 -->
      <input
        v-else-if="editingColumn?.fieldType === 'date'"
        ref="editorInput"
        v-model="editValue"
        type="date"
        class="editor-input"
        @keyup.enter="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      />

      <!-- 日期时间选择 -->
      <input
        v-else-if="editingColumn?.fieldType === 'datetime'"
        ref="editorInput"
        v-model="editValue"
        type="datetime-local"
        class="editor-input"
        @keyup.enter="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      />

      <!-- 链接输入 -->
      <input
        v-else-if="editingColumn?.fieldType === 'link'"
        ref="editorInput"
        v-model="editValue"
        type="url"
        class="editor-input"
        :placeholder="editingColumn?.placeholder || '请输入链接'"
        @keyup.enter="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      />

      <!-- 邮箱输入 -->
      <input
        v-else-if="editingColumn?.fieldType === 'email'"
        ref="editorInput"
        v-model="editValue"
        type="email"
        class="editor-input"
        :placeholder="editingColumn?.placeholder || '请输入邮箱'"
        @keyup.enter="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      />

      <!-- 电话输入 -->
      <input
        v-else-if="editingColumn?.fieldType === 'phone'"
        ref="editorInput"
        v-model="editValue"
        type="tel"
        class="editor-input"
        :placeholder="editingColumn?.placeholder || '请输入电话'"
        @keyup.enter="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      />

      <!-- 默认文本输入 -->
      <input
        v-else
        ref="editorInput"
        v-model="editValue"
        type="text"
        class="editor-input"
        :placeholder="editingColumn?.placeholder"
        @keyup.enter="handleEditConfirm"
        @keyup.escape="handleEditCancel"
        @blur="handleEditConfirm"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ListTable } from '@visactor/vue-vtable'
import type {
  VTableProps,
  VTableColumn,
  CellEditEvent,
  CellValidationResult
} from '../types'

// Props
const props = withDefaults(defineProps<VTableProps>(), {
  height: 400,
  width: 800,
  loading: false,
  editable: true,
  striped: true,
  bordered: true,
  showHeader: true,
  frozenColCount: 0,
  frozenRowCount: 0,
  allowResizeColumn: true,
  allowSortColumn: true,
  theme: 'default'
})

// Emits
const emit = defineEmits<{
  cellEdit: [event: CellEditEvent]
  cellValidate: [field: string, value: any, callback: (result: CellValidationResult) => void]
}>()

// 响应式数据
const tableRef = ref()
const inlineEditor = ref<HTMLElement>()
const editorInput = ref<HTMLInputElement>()
const editValue = ref<any>()
const editingCell = ref<{
  rowIndex: number
  colIndex: number
  field: string
  oldValue: any
  rect?: DOMRect
}>()
const editingColumn = ref<VTableColumn>()

// 表格配置
const tableOptions = computed(() => {
  return {
    columns: props.columns.map(col => ({
      field: col.field,
      title: col.title,
      width: col.width ？？ 120,
      cellType: getCellType(col.fieldType),
      style: {
        textAlign: col.align || 'left'
      }
    })),
    widthMode: 'standard' as const,
    heightMode: 'autoHeight' as const,
    autoWrapText: true,
    theme: getTheme()
  }
})

// 编辑器样式
const editorStyle = computed(() => {
  if (!editingCell.value?.rect) return {}

  const rect = editingCell.value.rect
  return {
    position: 'absolute' as const,
    left: rect.left + 'px',
    top: rect.top + 'px',
    width: rect.width + 'px',
    height: rect.height + 'px',
    zIndex: 1000
  }
})

// 获取单元格类型
const getCellType = (fieldType?: string) => {
  switch (fieldType) {
    case 'checkbox':
      return 'checkbox'
    case 'link':
      return 'link'
    default:
      return 'text'
  }
}

// 获取主题配置
const getTheme = () => {
  const themes = {
    default: 'DEFAULT',
    dark: 'DARK',
    bright: 'BRIGHT'
  }
  return themes[props.theme] || 'DEFAULT'
}

// 处理双击单元格事件
const handleDblClickCell = (args: any) => {
  if (!props.editable) return

  const { col, row } = args
  const column = props.columns[col]

  if (!column?.editable) return

  // 获取单元格位置
  const cellRect = getCellRect(row, col)

  // 设置编辑状态
  editingCell.value = {
    rowIndex: row,
    colIndex: col,
    field: column.field,
    oldValue: props.data[row]?.[column.field],
    rect: cellRect
  }
  editingColumn.value = column
  editValue.value = getEditValue(props.data[row]?.[column.field], column.fieldType)

  // 下一帧聚焦输入框
  nextTick(() => {
    if (editorInput.value) {
      editorInput.value.focus()
      if (column.fieldType === 'text' || column.fieldType === 'email' || column.fieldType === 'phone' || column.fieldType === 'link') {
        editorInput.value.select()
      }
    }
  })
}

// 处理单击单元格事件
const handleClickCell = (args: any) => {
  // 可以在这里添加单击处理逻辑
  console.log('单击单元格:', args)
}

// 获取单元格位置
const getCellRect = (row: number, col: number): DOMRect => {
  // 这里需要根据 VTable 的实际 DOM 结构来获取单元格位置
  // 暂时返回一个默认位置，实际使用时需要根据 VTable API 获取
  const rect = tableRef.value?.$el?.getBoundingClientRect()
  if (!rect) return new DOMRect()

  return new DOMRect(
    rect.left + col * 120, // 假设每列宽度 120px
    rect.top + (row + 1) * 40, // 假设每行高度 40px，+1 是因为表头
    120, // 宽度
    40   // 高度
  )
}

// 获取编辑值
const getEditValue = (value: any, fieldType?: string) => {
  if (value === null || value === undefined) {
    return fieldType === 'checkbox' ? false :
           fieldType === 'multiSelect' ? [] : ''
  }
  return value
}

// 处理编辑确认
const handleEditConfirm = async () => {
  if (!editingCell.value || !editingColumn.value) return

  const { rowIndex, colIndex, field, oldValue } = editingCell.value
  const newValue = editValue.value

  try {
    // 验证数据
    if (editingColumn.value.validator) {
      const validationResult = editingColumn.value.validator(newValue)
      if (validationResult !== true) {
        ElMessage.error(typeof validationResult === 'string' ? validationResult : '数据验证失败')
        return
      }
    }

    // 必填验证
    if (editingColumn.value.required && (newValue === '' || newValue === null || newValue === undefined)) {
      ElMessage.error(`${editingColumn.value.title} 为必填项`)
      return
    }

    // 更新数据
    const record = props.data[rowIndex]
    if (record) {
      record[field] = newValue

      // 刷新表格 - Vue VTable 会自动响应数据变化

      // 触发编辑事件
      const editEvent: CellEditEvent = {
        field,
        rowIndex,
        colIndex,
        oldValue,
        newValue,
        record
      }
      emit('cellEdit', editEvent)

      ElMessage.success('编辑成功')
    }

    // 关闭编辑器
    resetEditState()
  } catch (error) {
    console.error('编辑失败:', error)
    ElMessage.error('编辑失败，请重试')
  }
}

// 处理编辑取消
const handleEditCancel = () => {
  resetEditState()
}

// 处理复选框点击
const handleCheckboxClick = () => {
  // 延迟一点执行，让复选框状态先更新
  setTimeout(() => {
    handleEditConfirm()
  }, 10)
}

// 重置编辑状态
const resetEditState = () => {
  editingCell.value = undefined
  editingColumn.value = undefined
  editValue.value = undefined
}

// Vue VTable 会自动处理数据和列的响应式更新，无需手动监听
</script>

<style scoped>
.vtable-wrapper {
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.vtable-container {
  width: 100%;
  height: 100%;
}

.vtable-hidden {
  visibility: hidden;
}

.vtable-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.edit-form {
  padding: 16px 0;
}

/* 内联编辑器样式 */
.inline-editor {
  position: absolute;
  z-index: 1000;
  background: white;
  border: 2px solid #409eff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.editor-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  padding: 4px 8px;
  font-size: 14px;
  background: transparent;
  box-sizing: border-box;
}

.editor-select {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  padding: 4px 8px;
  font-size: 14px;
  background: white;
  box-sizing: border-box;
}

.editor-multiselect {
  height: auto;
  min-height: 100%;
  max-height: 120px;
  overflow-y: auto;
}

.editor-checkbox {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  font-size: 14px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.editor-checkbox input[type="checkbox"] {
  margin-right: 6px;
}
</style>
