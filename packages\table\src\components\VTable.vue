<template>
  <div class="relative border border-gray-300 rounded">
    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="absolute inset-0 flex flex-col items-center justify-center bg-white bg-opacity-90 z-10"
    >
      <div
        class="w-8 h-8 border-3 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-2"
      />
      <span class="text-gray-600">加载中...</span>
    </div>

    <!-- VTable 组件 -->
    <VListTable v-if="!loading" ref="tableRef" :options="tableOptions">
      <VListColumn
        v-for="column in tableColumns"
        :key="column.field"
        :field="column.field"
        :title="column.title"
        :width="column.width"
        editor="dynamic-render-editor"
      >
        <template #customLayout="{}">
          <div>123</div>
        </template>

        <template #edit="{ value, onChange }">
          <!-- 文本输入 -->
          <el-input
            v-if="column.fieldType === 'text'"
            :model-value="value"
            size="small"
            :placeholder="column.placeholder"
            @update:model-value="onChange"
            @keyup.enter="onChange"
            @blur="onChange"
          />

          <!-- 数字输入 -->
          <el-input-number
            v-else-if="column.fieldType === 'number'"
            :model-value="value"
            size="small"
            :min="column.min"
            :max="column.max"
            :step="column.step || 1"
            :precision="column.precision"
            :placeholder="column.placeholder"
            style="width: 100%"
            @update:model-value="onChange"
            @blur="onChange"
          />

          <!-- 单选下拉 -->
          <el-select
            v-else-if="column.fieldType === 'select'"
            :model-value="value"
            size="small"
            :placeholder="column.placeholder || '请选择'"
            style="width: 100%"
            @update:model-value="onChange"
            @blur="onChange"
          >
            <el-option
              v-for="option in column.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>

          <!-- 多选下拉 -->
          <el-select
            v-else-if="column.fieldType === 'multiSelect'"
            :model-value="value"
            multiple
            size="small"
            :placeholder="column.placeholder || '请选择'"
            style="width: 100%"
            @update:model-value="onChange"
            @blur="onChange"
          >
            <el-option
              v-for="option in column.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>

          <!-- 复选框 -->
          <el-checkbox
            v-else-if="column.fieldType === 'checkbox'"
            :model-value="value"
            size="small"
            @update:model-value="onChange"
            @blur="onChange"
          >
            {{ column.placeholder || '选中' }}
          </el-checkbox>

          <!-- 日期选择 -->
          <el-date-picker
            v-else-if="column.fieldType === 'date'"
            :model-value="value"
            type="date"
            size="small"
            :placeholder="column.placeholder || '选择日期'"
            :format="column.format || 'YYYY-MM-DD'"
            style="width: 100%"
            @update:model-value="onChange"
            @blur="onChange"
          />

          <!-- 日期时间选择 -->
          <el-date-picker
            v-else-if="column.fieldType === 'datetime'"
            :model-value="value"
            type="datetime"
            size="small"
            :placeholder="column.placeholder || '选择日期时间'"
            :format="column.format || 'YYYY-MM-DD HH:mm:ss'"
            style="width: 100%"
            @update:model-value="onChange"
            @blur="onChange"
          />

          <!-- 链接输入 -->
          <el-input
            v-else-if="column.fieldType === 'link'"
            :model-value="value"
            size="small"
            :placeholder="column.placeholder || '请输入链接'"
            @update:model-value="onChange"
            @keyup.enter="onChange"
            @blur="onChange"
          />

          <!-- 邮箱输入 -->
          <el-input
            v-else-if="column.fieldType === 'email'"
            :model-value="value"
            type="email"
            size="small"
            :placeholder="column.placeholder || '请输入邮箱'"
            @update:model-value="onChange"
            @keyup.enter="onChange"
            @blur="onChange"
          />

          <!-- 电话输入 -->
          <el-input
            v-else-if="column.fieldType === 'phone'"
            :model-value="value"
            size="small"
            :placeholder="column.placeholder || '请输入电话'"
            @update:model-value="onChange"
            @keyup.enter="onChange"
            @blur="onChange"
          />

          <!-- 默认文本输入 -->
          <el-input
            v-else
            :model-value="value"
            size="small"
            :placeholder="column.placeholder"
            @update:model-value="onChange"
            @keyup.enter="onChange"
            @blur="onChange"
          />
        </template>
      </VListColumn>
    </VListTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { VTableProps } from '../types'

// Props
const props = withDefaults(defineProps<VTableProps>(), {
  height: 400,
  width: 800,
  loading: false,
  editable: true,
  striped: true,
  bordered: true,
  showHeader: true,
  frozenColCount: 0,
  frozenRowCount: 0,
  allowResizeColumn: true,
  allowSortColumn: true,
  theme: 'default',
})

// Emits
const emit = defineEmits<{
  cellEdit: [event: CellEditEvent]
  cellValidate: [field: string, value: unknown, callback: (result: CellValidationResult) => void]
}>()

// 响应式数据
const tableRef = ref()

// 表格配置
const tableOptions = computed(() => {
  return {
    // columns: props.columns.map((col) => ({
    //   field: col.field,
    //   title: col.title,
    //   width: col.width ?? 120,
    //   cellType: getCellType(col.fieldType),
    //   // style: {
    //   //   textAlign: col.align ?? 'left',
    //   // },
    // })),
    editCellTrigger: 'click',
    records: Array.from(props.data),
    // widthMode: 'standard' as const,
    // heightMode: 'autoHeight' as const,
    // autoWrapText: true,
    // theme: getTheme(),
  }
})

const tableColumns = computed(() => {
  return props.columns.map((col) => ({
    ...col, // 保留所有原始属性
    width: col.width ?? 120,
    cellType: getCellType(col.fieldType),
    // style: {
    //   textAlign: col.align ?? 'left',
    // },
  }))
})

// 获取单元格类型
const getCellType = (fieldType?: string) => {
  switch (fieldType) {
    case 'checkbox':
      return 'checkbox'
    case 'link':
      return 'link'
    default:
      return 'text'
  }
}

// VTable 会自动处理编辑功能，无需手动实现
</script>

