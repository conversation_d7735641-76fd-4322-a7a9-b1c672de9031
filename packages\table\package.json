{"name": "@components/table", "version": "1.0.0", "private": false, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./style.css": "./dist/style.css"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "format": "prettier --write src/", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@visactor/vtable": "^1.19.2", "element-plus": "^2.10.2", "vue": "^3.5.17"}, "peerDependencies": {"vue": "^3.5.17"}, "devDependencies": {"@components/eslint-config": "workspace:*", "@playwright/test": "^1.53.1", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.16", "eslint": "^9.29.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "postcss": "^8.4.32", "prettier": "3.5.3", "rimraf": "^5.0.5", "tailwindcss": "^3.4.0", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-dts": "^3.6.4", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}, "volta": {"node": "22.17.0"}}