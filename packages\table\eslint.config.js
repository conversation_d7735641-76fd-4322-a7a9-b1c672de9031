// @ts-check
import vueConfig from '@components/eslint-config/vue.js'

export default [
  {
    name: 'table/ignores',
    ignores: [
      '**/dist/**',
      '**/dist-ssr/**',
      '**/coverage/**',
      '**/node_modules/**',
      '*.config.js',
      '*.config.mjs',
      'postcss.config.js',
      'tailwind.config.js',
    ],
  },

  // 使用我们的 Vue + TypeScript 配置
  ...vueConfig,

  // 项目特定的规则覆盖
  {
    name: 'table/overrides',
    rules: {
      // 组件库特定的规则
      'vue/multi-word-component-names': 'off', // 组件库可能有单词组件名
      '@typescript-eslint/no-explicit-any': 'warn', // 组件库中稍微严格一些
    },
  },
]
