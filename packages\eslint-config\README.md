# @components/eslint-config

一个实用的 ESLint 配置，专为 TypeScript 项目设计，专注于代码质量和一致性，而不会过于严格。

## 设计理念

此 ESLint 配置基于以下原则设计：

- **实用优于完美**：帮助捕获真正的错误并提高代码质量的规则
- **开发者友好**：没有过于严格的规则来中断开发流程
- **灵活性**：允许常见模式，不强制执行过于主观的样式
- **现代化**：支持最新的 JavaScript/TypeScript 特性和框架

## 主要特性

- ✅ **允许未使用的变量** - 开发期间不对未使用的导入/变量发出警告
- ✅ **灵活的类型定义** - 在需要时允许使用 `any` 类型
- ✅ **控制台语句** - 允许 `console.log`，仅显示警告
- ✅ **现代语法** - 支持 ES2022+ 特性
- ✅ **框架支持** - 为 Vue 3 和 React 提供专门的配置
- ✅ **TypeScript 优先** - 针对 TypeScript 开发进行优化

## 安装

```bash
# 使用 pnpm（推荐）
pnpm add -D @components/eslint-config

# 使用 npm
npm install --save-dev @components/eslint-config

# 使用 yarn
yarn add -D @components/eslint-config
```

### 对等依赖

您还需要安装所需的对等依赖：

```bash
# TypeScript 项目
pnpm add -D eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser

# Vue 项目（额外）
pnpm add -D eslint-plugin-vue

# React 项目（额外）
pnpm add -D eslint-plugin-react eslint-plugin-react-hooks
```

## 使用方法

### 基础 JavaScript/TypeScript

在项目根目录创建 `.eslintrc.js` 文件：

```javascript
module.exports = {
  extends: ['@components/eslint-config'],
  // 在这里添加您的自定义规则
};
```

### TypeScript 项目

```javascript
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  parserOptions: {
    project: './tsconfig.json',
  },
  // 在这里添加您的自定义规则
};
```

### Vue 3 + TypeScript

```javascript
module.exports = {
  extends: ['@components/eslint-config/vue'],
  parserOptions: {
    project: './tsconfig.json',
  },
  // 在这里添加您的自定义规则
};
```

### React + TypeScript

```javascript
module.exports = {
  extends: ['@components/eslint-config/react'],
  parserOptions: {
    project: './tsconfig.json',
  },
  // 在这里添加您的自定义规则
};
```

## 可用配置

| 配置                                   | 描述                 | 使用场景     |
| -------------------------------------- | -------------------- | ------------ |
| `@components/eslint-config`            | 基础 JavaScript 规则 | 基础 JS 项目 |
| `@components/eslint-config/typescript` | TypeScript 规则      | TS 项目      |
| `@components/eslint-config/vue`        | Vue 3 + TypeScript   | Vue 3 项目   |
| `@components/eslint-config/react`      | React + TypeScript   | React 项目   |

## 规则亮点

### 允许的内容（与严格配置不同）

- ✅ 未使用的变量和导入
- ✅ `console.log` 语句（带警告）
- ✅ TypeScript 中的 `any` 类型
- ✅ 非空断言（`!`）
- ✅ 空函数
- ✅ 魔法数字
- ✅ Vue 中的单词组件名

### 强制执行的内容

- ❌ `eval()` 和类似的危险函数
- ❌ `var` 声明（使用 `const`/`let`）
- ❌ 重复的对象键
- ❌ 不可达代码
- ❌ 无效的 typeof 比较
- ❌ React Hooks 规则违规

## 包脚本

将这些脚本添加到您的 `package.json` 中：

```json
{
  "scripts": {
    "lint": "eslint . --ext .js,.ts,.vue,.jsx,.tsx",
    "lint:fix": "eslint . --ext .js,.ts,.vue,.jsx,.tsx --fix"
  }
}
```

## 高级配置

### 自定义规则

您可以在项目中覆盖任何规则：

```javascript
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  rules: {
    // 将未使用变量设为错误而不是关闭
    '@typescript-eslint/no-unused-vars': 'error',

    // 禁用控制台警告
    'no-console': 'off',

    // 添加您的自定义规则
    'prefer-const': 'error',
  },
};
```

### 环境特定配置

```javascript
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  env: {
    browser: true,
    node: true,
    jest: true,
  },
  overrides: [
    {
      // 生产文件的更严格规则
      files: ['src/**/*.ts'],
      rules: {
        'no-console': 'error',
        '@typescript-eslint/no-unused-vars': 'error',
      },
    },
    {
      // 测试文件的宽松规则
      files: ['**/*.test.ts', '**/*.spec.ts'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'no-console': 'off',
      },
    },
  ],
};
```

### Monorepo 设置

对于 monorepo，您可以为不同的包使用不同的配置：

```javascript
// 根目录 .eslintrc.js
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  overrides: [
    {
      files: ['packages/vue-app/**/*.{ts,vue}'],
      extends: ['@components/eslint-config/vue'],
    },
    {
      files: ['packages/react-app/**/*.{ts,tsx}'],
      extends: ['@components/eslint-config/react'],
    },
  ],
};
```

## 工具集成

### VS Code

安装 ESLint 扩展并添加到您的 `.vscode/settings.json`：

```json
{
  "eslint.validate": [
    "javascript",
    "typescript",
    "vue",
    "javascriptreact",
    "typescriptreact"
  ],
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### Prettier 集成

此配置设计为与 Prettier 配合使用。安装 prettier 并创建 `.prettierrc`：

```json
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5"
}
```

### 预提交钩子

使用 husky 和 lint-staged：

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{js,ts,vue,jsx,tsx}": ["eslint --fix", "git add"]
  }
}
```

## 迁移指南

### 从标准配置迁移

如果从 `@typescript-eslint/recommended` 或类似的严格配置迁移：

1. 从 extends 数组中删除旧配置
2. 添加 `@components/eslint-config/typescript`
3. 删除现在不必要的规则覆盖
4. 测试您的构建 - 您应该看到更少的 linting 错误

### 从无 Linting 迁移

如果向现有项目添加 linting：

1. 安装包和对等依赖
2. 从适合您框架的基础配置开始
3. 运行 `npm run lint` 查看当前问题
4. 首先修复关键错误（通常是实际的错误）
5. 在时间允许的情况下逐步解决警告

## 贡献

发现规则过于严格或过于宽松？请提交 issue 并包含：

- 规则名称
- 为什么应该更改
- 演示问题的示例代码
- 建议的新设置

## 许可证

MIT
