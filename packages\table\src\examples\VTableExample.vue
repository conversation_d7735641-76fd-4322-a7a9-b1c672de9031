<template>
  <div class="vtable-example">
    <h1>VTable 多维表格示例</h1>
    
    <div class="example-section">
      <h2>基本用法</h2>
      <p>支持多种字段类型：文本、数字、单选、多选、复选框、日期、链接等</p>
      
      <VTable
        :columns="columns"
        :data="tableData"
        :width="1000"
        :height="400"
        :editable="true"
        @cell-edit="handleCellEdit"
      />
    </div>

    <div class="example-section">
      <h2>操作按钮</h2>
      <div class="button-group">
        <el-button @click="addRow">添加行</el-button>
        <el-button @click="removeRow">删除最后一行</el-button>
        <el-button @click="exportData">导出数据</el-button>
        <el-button @click="resetData">重置数据</el-button>
      </div>
    </div>

    <div class="example-section">
      <h2>当前数据</h2>
      <pre>{{ JSON.stringify(tableData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElButton } from 'element-plus'
import VTable from '../components/VTable.vue'
import type { VTableColumn, CellEditEvent } from '../types'

// 列配置
const columns: VTableColumn[] = [
  {
    field: 'id',
    title: 'ID',
    width: 80,
    fieldType: 'number',
    editable: false
  },
  {
    field: 'name',
    title: '姓名',
    width: 120,
    fieldType: 'text',
    editable: true,
    required: true,
    placeholder: '请输入姓名'
  },
  {
    field: 'age',
    title: '年龄',
    width: 100,
    fieldType: 'number',
    editable: true,
    min: 0,
    max: 150,
    placeholder: '请输入年龄'
  },
  {
    field: 'gender',
    title: '性别',
    width: 100,
    fieldType: 'select',
    editable: true,
    options: [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' },
      { label: '其他', value: 'other' }
    ],
    placeholder: '请选择性别'
  },
  {
    field: 'skills',
    title: '技能',
    width: 150,
    fieldType: 'multiSelect',
    editable: true,
    options: [
      { label: 'JavaScript', value: 'js' },
      { label: 'TypeScript', value: 'ts' },
      { label: 'Vue', value: 'vue' },
      { label: 'React', value: 'react' },
      { label: 'Node.js', value: 'node' },
      { label: 'Python', value: 'python' }
    ],
    placeholder: '请选择技能'
  },
  {
    field: 'active',
    title: '激活状态',
    width: 100,
    fieldType: 'checkbox',
    editable: true
  },
  {
    field: 'birthday',
    title: '生日',
    width: 120,
    fieldType: 'date',
    editable: true,
    format: 'YYYY-MM-DD',
    placeholder: '请选择生日'
  },
  {
    field: 'joinTime',
    title: '入职时间',
    width: 160,
    fieldType: 'datetime',
    editable: true,
    format: 'YYYY-MM-DD HH:mm:ss',
    placeholder: '请选择入职时间'
  },
  {
    field: 'website',
    title: '个人网站',
    width: 150,
    fieldType: 'link',
    editable: true,
    placeholder: '请输入网站链接'
  },
  {
    field: 'email',
    title: '邮箱',
    width: 180,
    fieldType: 'email',
    editable: true,
    placeholder: '请输入邮箱地址'
  },
  {
    field: 'phone',
    title: '电话',
    width: 130,
    fieldType: 'phone',
    editable: true,
    placeholder: '请输入电话号码'
  }
]

// 表格数据
const tableData = reactive([
  {
    id: 1,
    name: '张三',
    age: 28,
    gender: 'male',
    skills: ['js', 'vue'],
    active: true,
    birthday: '1995-06-15',
    joinTime: '2023-01-15 09:00:00',
    website: 'https://zhangsan.dev',
    email: '<EMAIL>',
    phone: '13800138001'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    gender: 'female',
    skills: ['ts', 'react', 'node'],
    active: false,
    birthday: '1991-03-22',
    joinTime: '2022-08-10 10:30:00',
    website: 'https://lisi.com',
    email: '<EMAIL>',
    phone: '13900139002'
  },
  {
    id: 3,
    name: '王五',
    age: 25,
    gender: 'male',
    skills: ['python', 'js'],
    active: true,
    birthday: '1998-11-08',
    joinTime: '2023-05-20 14:15:00',
    website: '',
    email: '<EMAIL>',
    phone: '13700137003'
  }
])

// 原始数据备份
const originalData = JSON.parse(JSON.stringify(tableData))

// 处理单元格编辑事件
const handleCellEdit = (event: CellEditEvent) => {
  console.log('单元格编辑事件:', event)
  ElMessage.success(`成功修改 ${event.field} 字段`)
}

// 添加行
const addRow = () => {
  const newId = Math.max(...tableData.map(item => item.id)) + 1
  tableData.push({
    id: newId,
    name: '',
    age: 0,
    gender: '',
    skills: [],
    active: false,
    birthday: '',
    joinTime: '',
    website: '',
    email: '',
    phone: ''
  })
  ElMessage.success('添加行成功')
}

// 删除最后一行
const removeRow = () => {
  if (tableData.length > 0) {
    tableData.pop()
    ElMessage.success('删除行成功')
  } else {
    ElMessage.warning('没有可删除的行')
  }
}

// 导出数据
const exportData = () => {
  const dataStr = JSON.stringify(tableData, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'vtable-data.json'
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('数据导出成功')
}

// 重置数据
const resetData = () => {
  tableData.splice(0, tableData.length, ...JSON.parse(JSON.stringify(originalData)))
  ElMessage.success('数据重置成功')
}
</script>

<style scoped>
.vtable-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
}

.example-section h1 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.example-section h2 {
  color: #34495e;
  margin-bottom: 15px;
  font-size: 18px;
}

.example-section p {
  color: #7f8c8d;
  margin-bottom: 15px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
