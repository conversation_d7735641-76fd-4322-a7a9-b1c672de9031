export interface TableColumn {
  key: string
  title: string
  width?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  render?: (value: any, record: any, index: number) => any
}

export interface TableData {
  [key: string]: any
}

export interface TableProps {
  columns: TableColumn[]
  data: TableData[]
  loading?: boolean
  pagination?: boolean
  pageSize?: number
  showHeader?: boolean
  bordered?: boolean
  striped?: boolean
  hoverable?: boolean
}

// VTable 多维表格相关类型定义
export type FieldType =
  | 'text'
  | 'number'
  | 'select'
  | 'multiSelect'
  | 'checkbox'
  | 'date'
  | 'datetime'
  | 'link'
  | 'email'
  | 'phone'

export interface SelectOption {
  label: string
  value: any
  disabled?: boolean
}

export interface VTableColumn {
  field: string
  title: string
  width?: number
  fieldType?: FieldType
  editable?: boolean
  required?: boolean
  options?: SelectOption[] // 用于 select 和 multiSelect
  format?: string // 用于日期格式化
  validator?: (value: any) => boolean | string
  placeholder?: string
  min?: number // 用于 number 类型
  max?: number // 用于 number 类型
  step?: number // 用于 number 类型
  precision?: number // 用于 number 类型
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  resizable?: boolean
  frozen?: 'left' | 'right' | 'none'
}

export interface VTableData {
  [key: string]: any
}

export interface VTableProps {
  columns: VTableColumn[]
  data: VTableData[]
  height?: number
  width?: number
  loading?: boolean
  editable?: boolean
  striped?: boolean
  bordered?: boolean
  showHeader?: boolean
  frozenColCount?: number
  frozenRowCount?: number
  allowResizeColumn?: boolean
  allowSortColumn?: boolean
  theme?: 'default' | 'dark' | 'bright'
}

export interface CellEditEvent {
  field: string
  rowIndex: number
  colIndex: number
  oldValue: any
  newValue: any
  record: VTableData
}

export interface CellValidationResult {
  valid: boolean
  message?: string
}
