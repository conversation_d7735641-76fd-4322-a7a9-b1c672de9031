import type { App } from 'vue'
import DataTable from './components/DataTable.vue'
import VTable from './components/VTable.vue'
import './style.css'

// 导出组件
export { DataTable, VTable }

// 导出类型
export type {
  TableColumn,
  TableData,
  TableProps,
  VTableColumn,
  VTableData,
  VTableProps,
  FieldType,
  SelectOption,
  CellEditEvent,
  CellValidationResult,
} from './types'

// 插件安装函数
export function install(app: App) {
  app.component('DataTable', DataTable)
  app.component('VTable', VTable)
}

// 默认导出
export default {
  install,
  DataTable,
  VTable,
}
