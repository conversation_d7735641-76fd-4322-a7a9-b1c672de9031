import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import VTableExample from './examples/VTableExample.vue'
import './style.css'
import { ListTable, ListColumn } from '@visactor/vue-vtable'

const app = createApp(VTableExample)
app.use(ElementPlus)

app.component('VListTable', ListTable)
app.component('VListColumn', ListColumn)

app.mount('#app')
